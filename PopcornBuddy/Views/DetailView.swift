import SwiftUI

struct DetailView: View {
    let mediaItem: MediaItem
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext
    
    @State private var isWatched = false
    @State private var userRating: Double = 0
    @State private var showingRatingSheet = false
    @State private var scrollOffset: CGFloat = 0
    @State private var headerOpacity: Double = 0
    
    private let headerHeight: CGFloat = 300
    
    var body: some View {
        ZStack {
            // Background
            Color.black.ignoresSafeArea()
            
            // Scrollable content
            ScrollView {
                VStack(spacing: 0) {
                    // Hero section with backdrop
                    heroSection
                    
                    // Content section
                    contentSection
                        .background(Color(.systemBackground))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .offset(y: -20)
                }
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                headerOpacity = min(max(Double(scrollOffset - 100)/100, 0), 1)
            }
            
            // Navigation header
            navigationHeader
        }
        .navigationBarHidden(true)
        .onAppear {
            setupInitialState()
        }
    }
    
    // MARK: - Hero Section
    private var heroSection: some View {
        GeometryReader { geometry in
            let offset = geometry.frame(in: .named("scroll")).minY
            let height = headerHeight + max(0, offset)
            
            ZStack(alignment: .bottom) {
                // Backdrop image
                AsyncImage(url: mediaItem.backdropURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                }
                .frame(width: geometry.size.width, height: height)
                .clipped()
                .offset(y: offset > 0 ? -offset : 0)
                
                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.1),
                        Color.black.opacity(0.8)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // Content overlay
                HStack(alignment: .bottom, spacing: 16) {
                    // Poster
                    AsyncImage(url: mediaItem.posterURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.3))
                    }
                    .frame(width: 120, height: 180)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .shadow(radius: 10)
                    
                    // Title and info
                    VStack(alignment: .leading, spacing: 8) {
                        Spacer()
                        
                        Text(mediaItem.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .lineLimit(3)
                        
                        HStack {
                            if let year = mediaItem.year {
                                Text(year)
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            
                            if let runtime = mediaItem.runtime {
                                Text("• \(runtime) min")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                        }
                        
                        // Rating
                        HStack {
                            AnimatedRatingStars(rating: mediaItem.rating / 2)
                            Text(String(format: "%.1f", mediaItem.rating))
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
            }
        }
        .frame(height: headerHeight)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Action buttons
            actionButtons
                .slideInAnimation(delay: 0.1)
            
            // Overview
            if !mediaItem.overview.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Overview")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(mediaItem.overview)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(nil)
                }
                .slideInAnimation(delay: 0.2)
            }
            
            // Cast section (placeholder)
            castSection
                .slideInAnimation(delay: 0.3)
            
            // Similar content section (placeholder)
            similarContentSection
                .slideInAnimation(delay: 0.4)
            
            Spacer(minLength: 100)
        }
        .padding(.horizontal, 20)
        .padding(.top, 30)
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: 16) {
            // Watch/Watched button
            Button(action: toggleWatchedStatus) {
                HStack {
                    Image(systemName: isWatched ? "checkmark.circle.fill" : "plus.circle.fill")
                    Text(isWatched ? "Watched" : "Add to Watchlist")
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(isWatched ? Color.green : Color.blue)
                )
            }
            
            // Rating button
            Button(action: { showingRatingSheet = true }) {
                HStack {
                    Image(systemName: "star.fill")
                    Text(userRating > 0 ? String(format: "%.1f", userRating) : "Rate")
                        .fontWeight(.medium)
                }
                .foregroundColor(.primary)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color.primary, lineWidth: 1)
                )
            }
            
            Spacer()
        }
    }
    
    // MARK: - Cast Section
    private var castSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cast")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<5, id: \.self) { _ in
                        VStack {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 60, height: 60)
                                .overlay(
                                    Image(systemName: "person.fill")
                                        .foregroundColor(.gray)
                                )
                            
                            Text("Actor Name")
                                .font(.caption)
                                .lineLimit(1)
                        }
                        .frame(width: 70)
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - Similar Content Section
    private var similarContentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("More Like This")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(0..<5, id: \.self) { _ in
                        AnimatedMediaCard(
                            title: "Similar Title",
                            subtitle: "2024",
                            imageURL: nil,
                            progress: nil,
                            isWatched: false,
                            onTap: {}
                        )
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - Navigation Header
    private var navigationHeader: some View {
        VStack {
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .backdrop(radius: 10)
                        )
                }
                
                Spacer()
                
                Text(mediaItem.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .opacity(headerOpacity)
                
                Spacer()
                
                Button(action: {}) {
                    Image(systemName: "ellipsis")
                        .font(.title2)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .backdrop(radius: 10)
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Spacer()
        }
        .background(
            Color(.systemBackground)
                .opacity(headerOpacity)
                .ignoresSafeArea(edges: .top)
        )
    }
    
    // MARK: - Helper Methods
    private func setupInitialState() {
        // Load existing data from Core Data
        // This would be implemented based on your Core Data setup
    }
    
    private func toggleWatchedStatus() {
        withAnimation(.spring()) {
            isWatched.toggle()
        }
        // Save to Core Data
    }
}

// MARK: - Supporting Types
struct MediaItem {
    let id: Int
    let title: String
    let overview: String
    let posterURL: URL?
    let backdropURL: URL?
    let rating: Double
    let year: String?
    let runtime: Int?
}

// MARK: - Scroll Offset Preference Key
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Backdrop Modifier
extension View {
    func backdrop(radius: CGFloat) -> some View {
        self.background(
            .ultraThinMaterial,
            in: Circle()
        )
    }
}

#Preview {
    DetailView(
        mediaItem: MediaItem(
            id: 1,
            title: "Sample Movie",
            overview: "This is a sample movie overview that describes the plot and main characters of the movie.",
            posterURL: nil,
            backdropURL: nil,
            rating: 8.5,
            year: "2024",
            runtime: 120
        )
    )
}
